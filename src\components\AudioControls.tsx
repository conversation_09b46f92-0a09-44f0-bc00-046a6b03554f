import React from "react";
import {
  Play,
  Pause,
  SkipBack as Ski<PERSON>,
  Volume2,
  Volume<PERSON>,
  <PERSON>wind,
  <PERSON>For<PERSON>,
  <PERSON><PERSON>ircle,
  Settings,
  AudioLines,
} from "lucide-react";

import {
  Box,
  IconButton,
  Slider,
  Typography,
  Paper,
  Menu,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
} from "@mui/material";
import { motion } from "framer-motion";
import type { VoiceOptions } from "../types";
import type { SelectChangeEvent } from "@mui/material/Select";
import { PageTitle } from "./dialog-title-underline";

interface AudioControlsProps {
  playing: boolean;
  paused: boolean;
  onPlay: () => void;
  onPause: () => void;
  onResume: () => void;
  onStop: () => void;
  onNext: () => void;
  onPrevious: () => void;
  voiceOptions: VoiceOptions;
  setVoiceOptions: (options: Partial<VoiceOptions>) => void;
  availableVoices: SpeechSynthesisVoice[];
}

const AudioControls: React.FC<AudioControlsProps> = ({
  playing,
  paused,
  onPlay,
  onPause,
  onResume,
  onStop,
  onNext,
  onPrevious,
  voiceOptions,
  setVoiceOptions,
  availableVoices,
}) => {
  const [settingsAnchorEl, setSettingsAnchorEl] =
    React.useState<null | HTMLElement>(null);
  const settingsOpen = Boolean(settingsAnchorEl);

  const handleSettingsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setSettingsAnchorEl(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchorEl(null);
  };

  const handleVoiceChange = (event: SelectChangeEvent<string>) => {
    const voiceURI = event.target.value;
    const selectedVoice =
      availableVoices.find((voice) => voice.voiceURI === voiceURI) || null;
    setVoiceOptions({ voice: selectedVoice });
  };

  // Animation variants for buttons
  const buttonVariants = {
    hover: { scale: 1.2, rotate: 5 },
    tap: { scale: 0.9 },
    disabled: { opacity: 0.5 },
  };

  // Animation variants for menu
  const menuVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  };

  return (
    <Box className="flex flex-col">
      <Box className="flex items-center justify-between mb-4">
        <Box className="flex items-center">
          <AudioLines size={32} className="mr-2" />
          <PageTitle title="TEXT TO SPEECH" />
        </Box>

        <Tooltip title="Adjust voice settings">
          <IconButton
            size="small"
            onClick={handleSettingsClick}
            aria-label="Voice settings"
            component={motion.button}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            <Settings size={18} />
          </IconButton>
        </Tooltip>

        <Menu
          anchorEl={settingsAnchorEl}
          open={settingsOpen}
          onClose={handleSettingsClose}
          PaperProps={{
            style: {
              width: 250,
              padding: "8px",
              borderRadius: 8,
            },
            component: motion.div,
            variants: menuVariants,
            initial: "hidden",
            animate: "visible",
          }}
        >
          <PageTitle title="Voice Settings" />

          <FormControl fullWidth margin="dense" size="small">
            <InputLabel id="voice-select-label">Voice</InputLabel>
            <Select
              labelId="voice-select-label"
              value={voiceOptions.voice?.voiceURI || ""}
              onChange={handleVoiceChange}
              label="Voice"
              sx={{ borderRadius: 2 }}
            >
              {availableVoices.map((voice) => (
                <MenuItem key={voice.voiceURI} value={voice.voiceURI}>
                  {voice.name} ({voice.lang})
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Box className="px-3 py-2">
            <Typography variant="body2" gutterBottom>
              Speed: {voiceOptions.rate.toFixed(1)}x
            </Typography>
            <Slider
              value={voiceOptions.rate}
              min={0.5}
              max={2}
              step={0.1}
              onChange={(_, value) =>
                setVoiceOptions({ rate: value as number })
              }
              aria-label="Speech rate"
              size="small"
              sx={{
                color: "#1C4C96",
                "& .MuiSlider-thumb": {
                  transition: "all 0.2s ease",
                  "&:hover": { boxShadow: "0 0 8px rgba(0,0,255,0.3)" },
                },
              }}
            />
          </Box>

          <Box className="px-3 py-2">
            <Typography variant="body2" gutterBottom>
              Pitch: {voiceOptions.pitch.toFixed(1)}
            </Typography>
            <Slider
              value={voiceOptions.pitch}
              min={0.5}
              max={2}
              step={0.1}
              onChange={(_, value) =>
                setVoiceOptions({ pitch: value as number })
              }
              aria-label="Speech pitch"
              size="small"
              sx={{
                color: "#1C4C96",
                "& .MuiSlider-thumb": {
                  transition: "all 0.2s ease",
                  "&:hover": { boxShadow: "0 0 8px rgba(0,0,255,0.3)" },
                },
              }}
            />
          </Box>
        </Menu>
      </Box>

      <Box className="flex items-center justify-between">
        <Box className="flex items-center">
          <Tooltip title="Previous Page">
            <IconButton
              onClick={onPrevious}
              aria-label="Previous"
              component={motion.button}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
              color="inherit"
            >
              <Rewind size={20} />
            </IconButton>
          </Tooltip>

          {playing && !paused ? (
            <Tooltip title="Pause">
              <IconButton
                onClick={onPause}
                aria-label="Pause"
                className="text-blue-600 mx-2"
                component={motion.button}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
                color="inherit"
              >
                <Pause size={24} />
              </IconButton>
            </Tooltip>
          ) : playing && paused ? (
            <Tooltip title="Resume">
              <IconButton
                onClick={onResume}
                aria-label="Resume"
                className="text-blue-600 mx-2"
                component={motion.button}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
                color="inherit"
              >
                <Play size={24} />
              </IconButton>
            </Tooltip>
          ) : (
            <Tooltip title="Play">
              <IconButton
                onClick={onPlay}
                aria-label="Play"
                className="text-blue-600 mx-2"
                component={motion.button}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
                color="inherit"
              >
                <Play size={24} />
              </IconButton>
            </Tooltip>
          )}

          <Tooltip title="Next Page">
            <IconButton
              onClick={onNext}
              aria-label="Next"
              className="text-blue-600"
              component={motion.button}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
              color="inherit"
            >
              <FastForward size={20} />
            </IconButton>
          </Tooltip>

          <Tooltip title="Stop">
            <IconButton
              onClick={onStop}
              aria-label="Stop"
              className="text-red-500 ml-2"
              disabled={!playing}
              component={motion.button}
              variants={buttonVariants}
              whileHover={playing ? "hover" : "disabled"}
              whileTap={playing ? "tap" : "disabled"}
              color="inherit"
            >
              <StopCircle size={20} />
            </IconButton>
          </Tooltip>
        </Box>

        <Box className="flex items-center w-1/3">
          {voiceOptions.volume === 0 ? (
            <VolumeX size={18} className="mr-2" />
          ) : (
            <Volume2 size={18} className="mr-2" />
          )}
          <Slider
            value={voiceOptions.volume}
            min={0}
            max={1}
            step={0.1}
            onChange={(_, value) =>
              setVoiceOptions({ volume: value as number })
            }
            aria-label="Volume"
            size="small"
            sx={{
              color: "#1C4C96",
              "& .MuiSlider-thumb": {
                transition: "all 0.2s ease",
                "&:hover": { boxShadow: "0 0 8px rgba(0,0,255,0.3)" },
              },
            }}
          />
        </Box>
      </Box>
    </Box>
    // </Paper>
  );
};

export default AudioControls;
