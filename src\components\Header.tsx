import React from "react";
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  useScrollTrigger,
  useMediaQuery,
  useTheme,
} from "@mui/material";

import TslsLogo from "../assets/Logo/TslsLogo.png";

interface HeaderProps {
  onHistoryClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onHistoryClick }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // For elevation on scroll
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 0,
  });

  return (
    <AppBar
      position="sticky"
      color="default"
      elevation={trigger ? 4 : 0}
      sx={{
        backgroundColor: "white",
        borderBottom: trigger ? "none" : `1px solid ${theme.palette.divider}`,
        transition: "all 0.3s ease-in-out",
      }}
    >
      <Toolbar sx={{ padding: isMobile ? theme.spacing(1) : theme.spacing(2) }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            flexGrow: 1,
            gap: theme.spacing(2),
          }}
        >
          <Box
            sx={{
              "&:hover": {
                opacity: 0.8,
              },
            }}
          >
            <img
              src={TslsLogo}
              alt="TSLS Logo"
              style={{
                maxHeight: "40px",
                height: "auto",
                transition: "opacity 0.3s ease",
                transformOrigin: "center",
                cursor: "pointer",
              }}
            />
          </Box>
          <Typography
            variant={isMobile ? "h6" : "h5"}
            component="h1"
            sx={{
              fontWeight: 600,
              color: theme.palette.grey[800],
              whiteSpace: "nowrap",
              "& .text-blue-600": {
                color: theme.palette.primary.main,
              },
            }}
          >
            TSLS Audio Handbook
          </Typography>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
