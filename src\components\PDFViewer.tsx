import React, { useEffect, useRef, useState, useMemo } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import HTMLFlipBook from "react-pageflip";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Box, IconButton, Paper, Typography, useMediaQuery, useTheme } from "@mui/material";
import type { PDFDocument, HighlightArea, TextItem } from "../types";
import WordHighlightOverlay from "./WordHighlightOverlay";

// Use a local worker script
import workerSrc from "pdfjs-dist/build/pdf.worker.min.js";
pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;

interface PDFViewerProps {
  document: PDFDocument | null;
  highlights: HighlightArea[];
  onPageChange: (pageNumber: number) => Promise<void>;
  onTextSelection: (text: string, pageIndex: number) => void;
  currentHighlight: TextItem | null;
  pageHeight: number;
  isReading?: boolean;
  textItems?: TextItem[];
  currentHighlightedWord?: TextItem | null;
  onWordClick?: (word: string, textItem: TextItem) => void;
  onSentenceClick?: (sentence: string, startIndex: number) => void;
  onParagraphClick?: (paragraph: string, startIndex: number) => void;
  speaking?: boolean;
  paused?: boolean;
  onWordHover?: (word: string, textItem: TextItem) => void;
  onSentenceHover?: (sentence: string, startIndex: number) => void;
  onParagraphHover?: (paragraph: string, startIndex: number) => void;
  readWords?: Set<string>;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  document,
  highlights,
  onPageChange,
  onTextSelection,
  currentHighlight,
  pageHeight,
  isReading = false,
  textItems = [],
  currentHighlightedWord,
  onWordClick,
  onSentenceClick,
  onParagraphClick,
  speaking,
  paused,
  onWordHover,
  onSentenceHover,
  onParagraphHover,
  readWords = new Set(),
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [renderedPages, setRenderedPages] = useState<Set<number>>(new Set([1]));
  const [pdfDimensions, setPdfDimensions] = useState<{ width: number; height: number } | null>(null);
  const flipBookRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const documentOptions = useMemo(
    () => ({
      cMapUrl: `/cmaps/`,
      cMapPacked: true,
      standardFontDataUrl: `/standard_fonts/`,
    }),
    []
  );

  useEffect(() => {
    if (document && document.pdfDoc) {
      const loadDimensions = async () => {
        try {
          const page = await document.pdfDoc.getPage(1);
          const viewport = page.getViewport({ scale: 1 });
          setPdfDimensions({
            width: viewport.width,
            height: viewport.height,
          });
        } catch (err) {
          console.error("Error fetching PDF dimensions:", err);
          setError("Failed to load PDF dimensions.");
        }
      };
      loadDimensions();
    }
  }, [document]);

  useEffect(() => {
    if (document) {
      setPageNumber(document.currentPage);
      setIsLoading(true);
      setError(null);
    }
  }, [document]);

  useEffect(() => {
    if (document && flipBookRef.current) {
      const pageFlipInstance = flipBookRef.current.pageFlip();
      const targetPage = document.currentPage - 1;
      const currentPage = pageFlipInstance.getCurrentPageIndex();
      if (currentPage !== targetPage) {
        pageFlipInstance.flip(targetPage, { duration: 0 });
      }
    }
  }, [document?.currentPage]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current && pdfDimensions) {
        const containerWidth = containerRef.current.offsetWidth - 18; // Account for 9px border
        const containerHeight = containerRef.current.offsetHeight - 18;
        const pdfAspectRatio = pdfDimensions.width / pdfDimensions.height;
        const containerAspectRatio = containerWidth / containerHeight;

        let newScale: number;
        if (pdfAspectRatio > containerAspectRatio) {
          newScale = containerWidth / pdfDimensions.width;
        } else {
          newScale = containerHeight / pdfDimensions.height;
        }

        newScale = Math.min(newScale * 0.9, 1.0);
        setScale(newScale);
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [pdfDimensions]);

  useEffect(() => {
    if (numPages && pageNumber) {
      const pagesToRender = new Set<number>();
      pagesToRender.add(pageNumber);
      if (pageNumber > 1) pagesToRender.add(pageNumber - 1);
      if (pageNumber < numPages) pagesToRender.add(pageNumber + 1);
      setRenderedPages(pagesToRender);
    }
  }, [pageNumber, numPages]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load the PDF document. Please ensure the file is valid and accessible.");
    setIsLoading(false);
  };

  const handlePageFlip = (e: any) => {
    const newPageNumber = e.data + 1;
    setPageNumber(newPageNumber);
    onPageChange(newPageNumber);
  };

  const goToPrevPage = () => {
    if (flipBookRef.current && pageNumber > 1) {
      flipBookRef.current.pageFlip().flipPrev();
    }
  };

  const goToNextPage = () => {
    if (flipBookRef.current && numPages !== null && pageNumber < numPages) {
      flipBookRef.current.pageFlip().flipNext();
    }
  };

  const handleTextSelection = (e: React.MouseEvent) => {
    const isClickable = !speaking || paused;
    if (!isClickable) return;
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      e.stopPropagation();
      onTextSelection(selection.toString(), pageNumber - 1);
      selection.removeAllRanges();
    }
  };

  const handleContainerClick = (e: React.MouseEvent) => {
    const isClickable = !speaking || paused;
    if (!isClickable) return;

    const target = e.target as HTMLElement;
    const textLayer = target.closest(".react-pdf__Page__textLayer");
    if (textLayer) {
      const selection = window.getSelection();
      if (selection && selection.toString()) {
        onTextSelection(selection.toString(), pageNumber - 1);
        selection.removeAllRanges();
      }
    }
  };

  if (!document) {
    return (
      <Paper elevation={3} sx={{ p: 6, textAlign: "center" }}>
        <Typography variant="h6">No document loaded</Typography>
        <Typography variant="body2" sx={{ mt: 2, color: "text.secondary" }}>
          Please upload a PDF document to get started
        </Typography>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper elevation={3} sx={{ p: 6, textAlign: "center" }}>
        <Typography variant="h6" color="error">
          Error
        </Typography>
        <Typography variant="body2" sx={{ mt: 2 }}>
          {error}
        </Typography>
      </Paper>
    );
  }

  const pages = Array.from(new Array(numPages || 0), (_, index) => {
    const pageNum = index + 1;
    const shouldRender = renderedPages.has(pageNum);
    const pageHighlights = highlights.filter((h) => h.pageIndex === pageNum - 1);

    return (
      <div key={index} className="page">
        {shouldRender && (
          <Box sx={{ position: "relative", width: "100%", height: "100%" }}>
            <Page
              key={`page_${pageNum}_${scale}`}
              pageNumber={pageNum}
              scale={scale}
              renderTextLayer={true}
              renderAnnotationLayer={false}
              className="page-content"
              onLoadSuccess={(page) => {
                page.getViewport({ scale: 1 });
              }}
              onLoadError={(error) => {
                console.error(`Error loading page ${pageNum}:`, error);
                setError(`Failed to load page ${pageNum}.`);
              }}
            />
            {pageHighlights.map(
              (highlight, idx) =>
                highlight.coordinates && (
                  <Box
                    key={`highlight_${idx}`}
                    sx={{
                      position: "absolute",
                      left: `${highlight.coordinates.x * scale}px`,
                      top: `${(pageHeight - highlight.coordinates.y - highlight.coordinates.height) * scale}px`,
                      width: `${highlight.coordinates.width * scale}px`,
                      height: `${highlight.coordinates.height * scale}px`,
                      backgroundColor: "rgba(255, 152, 0, 0.3)",
                      pointerEvents: "none",
                    }}
                  />
                )
            )}
            {currentHighlight && currentHighlight.pageIndex === pageNum - 1 && (
              <Box
                sx={{
                  position: "absolute",
                  left: `${currentHighlight.coordinates.x * scale}px`,
                  top: `${(pageHeight - currentHighlight.coordinates.y - currentHighlight.coordinates.height) * scale}px`,
                  width: `${currentHighlight.coordinates.width * scale}px`,
                  height: `${currentHighlight.coordinates.height * scale}px`,
                  background: "linear-gradient(135deg, rgba(255, 193, 7, 0.8) 0%, rgba(255, 235, 59, 0.9) 50%, rgba(255, 193, 7, 0.8) 100%)",
                  borderRadius: "4px",
                  border: "2px solid rgba(255, 152, 0, 0.6)",
                  boxShadow: "0 2px 8px rgba(255, 193, 7, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)",
                  pointerEvents: "none",
                  zIndex: 100,
                  animation: "pulse 1.5s ease-in-out infinite alternate",
                  transform: "scale(1.02)",
                  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  "&::before": {
                    content: '""',
                    position: "absolute",
                    top: "-2px",
                    left: "-2px",
                    right: "-2px",
                    bottom: "-2px",
                    background: "linear-gradient(45deg, rgba(255, 193, 7, 0.3), rgba(255, 235, 59, 0.3))",
                    borderRadius: "6px",
                    zIndex: -1,
                    filter: "blur(4px)",
                  },
                }}
              />
            )}
            <WordHighlightOverlay
              textItems={textItems}
              pageNumber={pageNum}
              scale={scale}
              pageHeight={pageHeight}
              currentHighlightedWord={currentHighlightedWord}
              onWordClick={onWordClick}
              onSentenceClick={onSentenceClick}
              onParagraphClick={onParagraphClick}
              onWordHover={onWordHover}
              onSentenceHover={onSentenceHover}
              onParagraphHover={onParagraphHover}
              isReading={isReading}
              speaking={speaking}
              paused={paused}
              readWords={readWords}
            />
          </Box>
        )}
      </div>
    );
  });

  return (
    <Box
      sx={{
        position: "relative",
        height: "100%",
        width: "100%",
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
        //border: "9px solid #000000",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          left: 0,
          top: 0,
          bottom: 0,
          display: "flex",
          alignItems: "center",
          zIndex: 10,
          px: 1,
        }}
      >
        <IconButton
          onClick={goToPrevPage}
          disabled={pageNumber <= 1 || isReading}
          aria-label="Previous page"
          sx={{
            backgroundColor: "rgba(255, 255, 255, 0.7)",
            "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.9)" },
          }}
        >
          <ChevronLeft fontSize="large" />
        </IconButton>
      </Box>
      <Box
        sx={{
          position: "absolute",
          right: 0,
          top: 0,
          bottom: 0,
          display: "flex",
          alignItems: "center",
          zIndex: 10,
          px: 1,
        }}
      >
        <IconButton
          onClick={goToNextPage}
          disabled={numPages === null || pageNumber >= numPages || isReading}
          aria-label="Next page"
          sx={{
            backgroundColor: "rgba(255, 255, 255, 0.7)",
            "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.9)" },
          }}
        >
          <ChevronRight fontSize="large" />
        </IconButton>
      </Box>
      <Box
        sx={{
          position: "absolute",
          bottom: 16,
          left: 0,
          right: 0,
          display: "flex",
          justifyContent: "center",
          zIndex: 10,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            px: 2,
            py: 1,
            backgroundColor: "rgba(255, 255, 255, 0.7)",
            borderRadius: 4,
          }}
        >
          <Typography variant="body2">
            Page {pageNumber} of {numPages || 0}
          </Typography>
        </Paper>
      </Box>
      <Box
        ref={containerRef}
        sx={{
          flexGrow: 1,
          position: "relative",
          overflow: "visible",
        }}
        onMouseUp={handleTextSelection}
        onClick={handleContainerClick}
      >
        <Document
          file={document.url}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          options={documentOptions}
        >
          <HTMLFlipBook
            className="pdf-flipbook"
            ref={flipBookRef}
            width={pdfDimensions ? pdfDimensions.width : isMobile ? 350 : 600}
            height={pdfDimensions ? pdfDimensions.height : isMobile ? 500 : 800}
            size="stretch"
            minWidth={300}
            maxWidth={800}
            minHeight={400}
            maxHeight={1000}
            drawShadow={true}
            flippingTime={500}
            startPage={pageNumber - 1}
            onFlip={handlePageFlip}
            showCover={false}
            style={{ margin: "0 auto", backgroundColor: "transparent" }}
            usePortrait={true}
            autoSize={true}
            maxShadowOpacity={0.3}
            mobileScrollSupport={true}
            startZIndex={0}
            clickEventForward={false}
            useMouseEvents={true}
            swipeDistance={30}
            showPageCorners={true}
            disableFlipByClick={true}
          >
            {pages}
          </HTMLFlipBook>
        </Document>
      </Box>
      <style>{`
        .page {
          background-color: #f5f5f5;
          background-image: linear-gradient(
              to bottom,
              rgba(0, 0, 0, 0.1) 0%,
              transparent 20%
            ),
            linear-gradient(
              to right,
              #e0e0e0 0%,
              #f5f5f5 5%,
              #f5f5f5 95%,
              #e0e0e0 100%
            );
          box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.1), 0 0 8px rgba(0, 0, 0, 0.1);
          padding: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: visible;
        }

        .page-content {
          background: white;
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: visible;
        }

        .page.--left {
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
          box-shadow: inset 5px 0 20px -5px rgba(0, 0, 0, 0.3);
        }

        .page.--right {
          border-top-right-radius: 3px;
          border-bottom-right-radius: 3px;
          box-shadow: inset -5px 0 20px -5px rgba(0, 0, 0, 0.3);
        }

        .page.--left .page-content {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }

        .page.--right .page-content {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }

        .page-shadow {
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.15) !important;
        }

        ::selection {
          background: rgba(255, 152, 0, 0.3);
          color: inherit;
        }

        .react-pdf__Page__textLayer {
          pointer-events: ${!speaking || paused ? "auto" : "none"};
          user-select: ${!speaking || paused ? "text" : "none"};
          ${!speaking || paused ? `
            cursor: text;
            background-color: rgba(33, 150, 243, 0.02);
            border: 1px dashed rgba(33, 150, 243, 0.3);
            border-radius: 4px;
          ` : ""}
        }

        ${!speaking || paused ? `
          .react-pdf__Page__textLayer::before {
            content: "${paused ? "⏸️ Hover or click to resume reading" : "📝 Hover or click to start reading"}";
            position: absolute;
            top: -25px;
            left: 0;
            right: 0;
            background: ${paused ? "rgba(255, 152, 0, 0.9)" : "rgba(33, 150, 243, 0.9)"};
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-align: center;
            zIndex: 1000;
            pointer-events: none;
          }
        ` : ""}

        .react-pdf__Page__canvas {
          pointer-events: none;
        }

        .stf__wrapper {
          width: 100% !important;
          height: 100% !important;
          overflow: visible;
        }

        .stf__block {
          width: 100% !important;
          height: 100% !important;
          overflow: visible;
        }

        @keyframes pulse {
          0% {
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: scale(1.02);
          }
          100% {
            box-shadow: 0 4px 16px rgba(255, 193, 7, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
          }
        }

        @keyframes wordHighlight {
          0% {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.6) 0%, rgba(255, 235, 59, 0.7) 50%, rgba(255, 193, 7, 0.6) 100%);
            transform: scale(1);
          }
          50% {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.9) 0%, rgba(255, 235, 59, 1) 50%, rgba(255, 193, 7, 0.9) 100%);
            transform: scale(1.03);
          }
          100% {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.6) 0%, rgba(255, 235, 59, 0.7) 50%, rgba(255, 193, 7, 0.6) 100%);
            transform: scale(1);
          }
        }

        @keyframes slideIn {
          0% {
            opacity: 0;
            transform: translateY(-10px) scale(0.9);
          }
          100% {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
      `}</style>
    </Box>
  );
};

export default PDFViewer;