import React, { useEffect, useRef, useState, useMemo } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import HTMLFlipBook from "react-pageflip";
import { Box, IconButton, Typography } from "@mui/material";
import { ChevronLeft, ChevronRight } from "lucide-react";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import workerSrc from "pdfjs-dist/build/pdf.worker.min.js";
import type { PDFDocument, HighlightArea, TextItem } from "../types";
import WordHighlightOverlay from "./WordHighlightOverlay";

pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;

interface PDFViewerProps {
  document: PDFDocument | null;
  highlights: HighlightArea[];
  onPageChange: (pageNumber: number) => Promise<void>;
  onTextSelection: (text: string, pageIndex: number) => void;
  currentHighlight: TextItem | null;
  pageHeight: number;
  isReading?: boolean;
  textItems?: TextItem[];
  currentHighlightedWord?: TextItem | null;
  onWordClick?: (word: string, textItem: TextItem) => void;
  onSentenceClick?: (sentence: string, startIndex: number) => void;
  onParagraphClick?: (paragraph: string, startIndex: number) => void;
  speaking?: boolean;
  paused?: boolean;
  onWordHover?: (word: string, textItem: TextItem) => void;
  onSentenceHover?: (sentence: string, startIndex: number) => void;
  onParagraphHover?: (paragraph: string, startIndex: number) => void;
  readWords?: Set<string>;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  document,
  highlights,
  onPageChange,
  onTextSelection,
  currentHighlight,
  pageHeight,
  isReading = false,
  textItems = [],
  currentHighlightedWord,
  onWordClick,
  onSentenceClick,
  onParagraphClick,
  speaking,
  paused,
  onWordHover,
  onSentenceHover,
  onParagraphHover,
  readWords = new Set(),
}) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfDimensions, setPdfDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const flipBookRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const documentOptions = useMemo(
    () => ({
      cMapUrl: `/cmaps/`,
      cMapPacked: true,
      standardFontDataUrl: `/standard_fonts/`,
    }),
    []
  );

  useEffect(() => {
    if (document) {
      const loadDimensions = async () => {
        try {
          const page = await document.pdfDoc!.getPage(1); // Use ! to assert non-null
          const viewport = page.getViewport({ scale: 1 });
          setPdfDimensions({
            width: viewport.width,
            height: viewport.height,
          });
        } catch (err) {
          console.error("Error fetching PDF dimensions:", err);
          setError("Failed to load PDF dimensions.");
        }
      };
      loadDimensions();
    }
  }, [document]);

  useEffect(() => {
    if (document) {
      setPageNumber(document.currentPage);
      setIsLoading(true);
      setError(null);
    }
  }, [document]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current && pdfDimensions) {
        const containerWidth = containerRef.current.offsetWidth;
        const containerHeight = containerRef.current.offsetHeight;
        const pdfAspectRatio = pdfDimensions.width / pdfDimensions.height;
        const containerAspectRatio = containerWidth / containerHeight;

        let newScale: number;
        if (pdfAspectRatio > containerAspectRatio) {
          newScale = containerWidth / pdfDimensions.width;
        } else {
          newScale = containerHeight / pdfDimensions.height;
        }
        setScale(Math.min(newScale * 0.95, 1.5));
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [pdfDimensions]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load the PDF document.");
    setIsLoading(false);
  };

  const handlePageFlip = (e: any) => {
    const newPageNumber = e.data + 1;
    setPageNumber(newPageNumber);
    onPageChange(newPageNumber);
  };

  const goToPrevPage = () => {
    if (flipBookRef.current && pageNumber > 1) {
      flipBookRef.current.pageFlip().flipPrev();
    }
  };

  const goToNextPage = () => {
    if (flipBookRef.current && numPages !== null && pageNumber < numPages) {
      flipBookRef.current.pageFlip().flipNext();
    }
  };

  const handleTextSelection = (e: React.MouseEvent) => {
    const isClickable = !speaking || paused;
    if (!isClickable) return;
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      e.stopPropagation();
      onTextSelection(selection.toString(), pageNumber - 1);
      selection.removeAllRanges();
    }
  };

  if (!document) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
        <Typography variant="h6" className="text-gray-600">
          No document loaded. Please upload a PDF.
        </Typography>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
        <Typography variant="h6" className="text-red-600">
          Error: {error}
        </Typography>
      </div>
    );
  }

  const pages = Array.from({ length: numPages || 0 }, (_, index) => {
    const pageNum = index + 1;
    const pageHighlights = highlights.filter(
      (h) => h.pageIndex === pageNum - 1
    );

    return (
      <div
        key={index}
        className="bg-white shadow-lg rounded-lg overflow-hidden"
      >
        <Page
          pageNumber={pageNum}
          scale={scale}
          renderTextLayer={true}
          renderAnnotationLayer={false}
          className="w-full h-full"
          onLoadSuccess={(page) => {
            page.getViewport({ scale: 1 });
          }}
          onLoadError={(error) => {
            console.error(`Error loading page ${pageNum}:`, error);
            setError(`Failed to load page ${pageNum}.`);
          }}
        />
        {pageHighlights.map(
          (highlight, idx) =>
            highlight.coordinates && (
              <div
                key={`highlight_${idx}`}
                className="absolute bg-yellow-300 bg-opacity-30 pointer-events-none"
                style={{
                  left: `${highlight.coordinates.x * scale}px`,
                  top: `${
                    (pageHeight -
                      highlight.coordinates.y -
                      highlight.coordinates.height) *
                    scale
                  }px`,
                  width: `${highlight.coordinates.width * scale}px`,
                  height: `${highlight.coordinates.height * scale}px`,
                }}
              />
            )
        )}
        {currentHighlight && currentHighlight.pageIndex === pageNum - 1 && (
          <div
            className="absolute bg-yellow-400 bg-opacity-60 border-2 border-yellow-600 rounded animate-pulse"
            style={{
              left: `${currentHighlight.coordinates.x * scale}px`,
              top: `${
                (pageHeight -
                  currentHighlight.coordinates.y -
                  currentHighlight.coordinates.height) *
                scale
              }px`,
              width: `${currentHighlight.coordinates.width * scale}px`,
              height: `${currentHighlight.coordinates.height * scale}px`,
            }}
          />
        )}
        <WordHighlightOverlay
          textItems={textItems}
          pageNumber={pageNum}
          scale={scale}
          pageHeight={pageHeight}
          currentHighlightedWord={currentHighlightedWord}
          onWordClick={onWordClick}
          onSentenceClick={onSentenceClick}
          onParagraphClick={onParagraphClick}
          onWordHover={onWordHover}
          onSentenceHover={onSentenceHover}
          onParagraphHover={onParagraphHover}
          isReading={isReading}
          speaking={speaking}
          paused={paused}
          readWords={readWords}
        />
      </div>
    );
  });

  return (
    <div className="relative w-full h-full flex flex-col bg-gray-100">
      <div className="absolute top-1/2 left-4 transform -translate-y-1/2 z-10">
        <IconButton
          onClick={goToPrevPage}
          disabled={pageNumber <= 1 || isReading}
          className="bg-white bg-opacity-70 hover:bg-opacity-90"
        >
          <ChevronLeft size={24} />
        </IconButton>
      </div>
      <div className="absolute top-1/2 right-4 transform -translate-y-1/2 z-10">
        <IconButton
          onClick={goToNextPage}
          disabled={numPages === null || pageNumber >= numPages || isReading}
          className="bg-white bg-opacity-70 hover:bg-opacity-90"
        >
          <ChevronRight size={24} />
        </IconButton>
      </div>
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10 bg-white bg-opacity-70 px-4 py-2 rounded-full">
        <Typography variant="body2">
          Page {pageNumber} of {numPages || 0}
        </Typography>
      </div>
      <div
        ref={containerRef}
        className="flex-grow overflow-hidden"
        onMouseUp={handleTextSelection}
      >
        <Document
          file={document.url}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          options={documentOptions}
        >
          <HTMLFlipBook
            ref={flipBookRef}
            width={pdfDimensions ? pdfDimensions.width : 600}
            height={pdfDimensions ? pdfDimensions.height : 800}
            size="stretch"
            minWidth={300}
            maxWidth={800}
            minHeight={400}
            maxHeight={1000}
            flippingTime={500}
            startPage={pageNumber - 1}
            onFlip={handlePageFlip}
            className="mx-auto"
            style={{ backgroundColor: "transparent" }}
            mobileScrollSupport={true}
          >
            {pages}
          </HTMLFlipBook>
        </Document>
      </div>
      <style>{`
        .react-pdf__Page__textLayer {
          pointer-events: ${!speaking || paused ? "auto" : "none"};
          user-select: ${!speaking || paused ? "text" : "none"};
        }
        .react-pdf__Page__canvas {
          pointer-events: none;
        }
        .stf__wrapper,
        .stf__block {
          width: 100% !important;
          height: 100% !important;
          overflow: visible;
        }
        @keyframes pulse {
          0% {
            transform: scale(1);
          }
          100% {
            transform: scale(1.02);
          }
        }
      `}</style>
    </div>
  );
};

export default PDFViewer;
