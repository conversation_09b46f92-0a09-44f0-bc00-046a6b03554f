import React from 'react';
import { Box, Paper, Typography } from '@mui/material';

interface TextHighlighterProps {
  text: string;
  highlightedText: string;
  currentWord: string;
}

const TextHighlighter: React.FC<TextHighlighterProps> = ({
  text,
  highlightedText,
  currentWord
}) => {
  // If there's no text or highlighted text, return null
  if (!text) return null;
  
  // If there's no highlighted text, display the entire text
  if (!highlightedText) {
    return (
      <Paper elevation={2} className="p-4 mb-4 max-h-60 overflow-y-auto">
        <Typography variant="body1">{text}</Typography>
      </Paper>
    );
  }
  
  // Get the index of the highlighted text within the full text
  const highlightIndex = text.indexOf(highlightedText);
  
  // If the highlighted text isn't found, display the entire text
  if (highlightIndex === -1) {
    return (
      <Paper elevation={2} className="p-4 mb-4 max-h-60 overflow-y-auto">
        <Typography variant="body1">{text}</Typography>
      </Paper>
    );
  }
  
  // Get the current word index within the highlighted text
  const currentWordIndex = currentWord ? highlightedText.indexOf(currentWord) : -1;
  
  // Split the text into three parts: before highlight, highlight, and after highlight
  const beforeHighlight = text.substring(0, highlightIndex);
  const afterHighlight = text.substring(highlightIndex + highlightedText.length);
  
  // If there's a current word, highlight it within the highlighted text
  let highlightedContent;
  if (currentWordIndex !== -1 && currentWord) {
    const beforeCurrentWord = highlightedText.substring(0, currentWordIndex);
    const afterCurrentWord = highlightedText.substring(currentWordIndex + currentWord.length);
    
    highlightedContent = (
      <>
        <span>{beforeCurrentWord}</span>
        <span className="bg-yellow-300 font-semibold">{currentWord}</span>
        <span>{afterCurrentWord}</span>
      </>
    );
  } else {
    highlightedContent = highlightedText;
  }
  
  return (
    <Paper elevation={2} className="p-4 mb-4 max-h-60 overflow-y-auto">
      <Typography variant="body1">
        {beforeHighlight}
        <span className="bg-blue-100">{highlightedContent}</span>
        {afterHighlight}
      </Typography>
    </Paper>
  );
};

export default TextHighlighter;