"use client";
interface PageTitleUnderLineProps {
  title: string;
  className?: string;
}

export function PageTitle({ title }: PageTitleUnderLineProps) {
  return (
    <div>
      <h1 className="text-lg font-bold text-center text-black">
        {title.toUpperCase()}
      </h1>
      <div className="flex justify-center mt-1 mb-2">
        <div className="flex gap-1">
          <div className="bg-[#8F1D36] h-1 w-20 rounded-lg" />
          <div className="bg-[#8F1D36] h-1 w-5 rounded-lg" />
          <div className="bg-[#8F1D36] h-1 w-3 rounded-lg" />
        </div>
      </div>
    </div>
  );
}
