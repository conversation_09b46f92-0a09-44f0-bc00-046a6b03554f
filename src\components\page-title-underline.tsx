"use client";
interface PageTitleUnderLineProps {
  title: string;
}

export function PageTitle({ title }: PageTitleUnderLineProps) {
  return (
    <div>
      <h1 className="text-2xl font-bold text-center text-black">
        {title.toUpperCase()}
      </h1>
      <div className="flex justify-center mt-2">
        <div className="flex gap-1">
          <div className="bg-[#8F1D36] h-2 w-36 rounded-lg" />
          <div className="bg-[#8F1D36] h-2 w-8 rounded-lg" />
          <div className="bg-[#8F1D36] h-2 w-4 rounded-lg" />
        </div>
      </div>
    </div>
  );
}
