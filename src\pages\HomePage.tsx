import React, { useState, useEffect, useCallback, useRef } from "react";
import { Container, Box, Paper, Typography } from "@mui/material";
import PDFViewer from "../components/PDFViewer";
import AudioControls from "../components/AudioControls";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { usePDFDocument } from "../hooks/usePDFDocument";
import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
import type { TextItem } from "../types";
import { extractTextFromPage } from "../utils/pdfHelpers";
import { PageTitle } from "../components/page-title-underline";

const HomePage: React.FC = () => {
  const {
    documentInfo,
    highlights,
    loadDocument,
    setCurrentPage,
    addHighlight,
    clearHighlights,
    isLoading,
  } = usePDFDocument();

  const {
    voices,
    options,
    speaking,
    paused,
    speak,
    pause,
    resume,
    stop,
    setOptions,
    speakFromPosition,
  } = useSpeechSynthesis();

  const [currentHighlightedText, setCurrentHighlightedText] = useState("");
  const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(
    null
  );
  const [currentHighlightedWord, setCurrentHighlightedWord] = useState<TextItem | null>(null);
  const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
  const [isAutoReading, setIsAutoReading] = useState(false);
  const [isStoppedAndReady, setIsStoppedAndReady] = useState(false);
  const [readWords, setReadWords] = useState<Set<string>>(new Set());
  const [hoveredReadingPosition, setHoveredReadingPosition] = useState<{
    text: string;
    startIndex: number;
    type: 'word' | 'sentence' | 'paragraph';
    textItem?: TextItem;
  } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadDocument("/src/assets/sample.pdf");
  }, [loadDocument]);





  const getPageTextAndItems = useCallback(
    async (pageNumber: number) => {
      if (!documentInfo || !documentInfo.pdfDoc)
        return { text: "", textItems: [] };

      // Read individual pages sequentially to ensure no pages are skipped
      const { fullText, textItems: pageTextItems } =
        await extractTextFromPage(documentInfo.pdfDoc, pageNumber);

      return { text: fullText, textItems: pageTextItems };
    },
    [documentInfo]
  );

  const handleTextSelection = useCallback(async (text: string, pageIndex: number) => {
    if (!text || !documentInfo || !documentInfo.pdfDoc) return;

    // Only allow text selection when audio is stopped or paused
    const isClickable = !speaking || paused;
    if (!isClickable) return;

    // Stop current reading if any
    if (speaking) {
      stop();
    }
    setIsAutoReading(false);
    setIsStoppedAndReady(false);

    // Get the full text for the current page
    const { text: fullText, textItems } = await getPageTextAndItems(documentInfo.currentPage);

    // Find the starting position of the selected text in the full text
    const selectedTextIndex = fullText.indexOf(text);
    if (selectedTextIndex === -1) {
      // Fallback: just speak the selected text directly
      setCurrentHighlightedText(text);
      addHighlight({ pageIndex, text });
      speak(
        text,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) => charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
      return;
    }

    // Start reading from the selected text position
    setCurrentHighlightedText(text);
    addHighlight({ pageIndex, text });
    speakFromPosition(
      fullText,
      selectedTextIndex,
      [],
      documentInfo.currentPage - 1,
      (event) => {
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const charIndex = event.charIndex;
          const currentTextItem = textItems.find(
            (item) => charIndex >= item.startIndex && charIndex < item.endIndex
          );
          if (currentTextItem) {
            setCurrentHighlightedWord(currentTextItem);
          }
        }
      },
      () => {
        setCurrentHighlightedWord(null);
      }
    );
  }, [documentInfo, speaking, paused, stop, getPageTextAndItems, speakFromPosition, addHighlight, speak]);

  const handleWordClick = useCallback(async (word: string, textItem: TextItem) => {
    if (!documentInfo || !documentInfo.pdfDoc) return;

    // Stop current reading
    if (speaking) {
      stop();
    }
    setIsAutoReading(false);
    setIsStoppedAndReady(false);

    // Get the full text for the current page
    const { text, textItems } = await getPageTextAndItems(documentInfo.currentPage);

    // Find the position of the clicked word in the full text
    const wordStartIndex = textItem.startIndex;

    // Start reading from the clicked word
    speakFromPosition(
      text,
      wordStartIndex,
      [],
      documentInfo.currentPage - 1,
      (event) => {
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const charIndex = event.charIndex;
          const currentTextItem = textItems.find(
            (item) => charIndex >= item.startIndex && charIndex < item.endIndex
          );
          if (currentTextItem) {
            setCurrentHighlightedWord(currentTextItem);
          }
        }
      },
      () => {
        setCurrentHighlightedWord(null);
      }
    );
  }, [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]);

  const handleSentenceClick = useCallback(async (sentence: string, startIndex: number) => {
    if (!documentInfo || !documentInfo.pdfDoc) return;

    // Stop current reading
    if (speaking) {
      stop();
    }
    setIsAutoReading(false);
    setIsStoppedAndReady(false);

    // Get the full text for the current page
    const { text, textItems } = await getPageTextAndItems(documentInfo.currentPage);

    // Start reading from the sentence
    speakFromPosition(
      text,
      startIndex,
      [],
      documentInfo.currentPage - 1,
      (event) => {
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const charIndex = event.charIndex;
          const currentTextItem = textItems.find(
            (item) => charIndex >= item.startIndex && charIndex < item.endIndex
          );
          if (currentTextItem) {
            setCurrentHighlightedWord(currentTextItem);
          }
        }
      },
      () => {
        setCurrentHighlightedWord(null);
      }
    );
  }, [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]);
  const handleParagraphClick = useCallback(async (paragraph: string, startIndex: number) => {
    if (!documentInfo || !documentInfo.pdfDoc) return;

    // Stop current reading
    if (speaking) {
      stop();
    }
    setIsAutoReading(false);
    setIsStoppedAndReady(false);

    // Get the full text for the current page
    const { text, textItems } = await getPageTextAndItems(documentInfo.currentPage);

    // Start reading from the paragraph
    speakFromPosition(
      text,
      startIndex,
      [],
      documentInfo.currentPage - 1,
      (event) => {
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const charIndex = event.charIndex;
          const currentTextItem = textItems.find(
            (item) => charIndex >= item.startIndex && charIndex < item.endIndex
          );
          if (currentTextItem) {
            setCurrentHighlightedWord(currentTextItem);
          }
        }
      },
      () => {
        setCurrentHighlightedWord(null);
      }
    );
  }, [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]);

  const handleWordHover = useCallback(async (word: string, textItem: TextItem) => {
    if (!documentInfo || !documentInfo.pdfDoc) return;

    // Always track hovered position for potential click-to-read
    if (textItem) {
      setHoveredReadingPosition({
        text: word,
        startIndex: textItem.startIndex,
        type: 'word',
        textItem: textItem
      });
    } else {
      setHoveredReadingPosition(null);
    }

    // Allow hover reading when audio is stopped or paused
    const canHover = !speaking || paused;
    if (!canHover) return;

    // Get the full text for the current page
    const { text, textItems } = await getPageTextAndItems(documentInfo.currentPage);

    // Find the position of the hovered word in the full text
    const wordStartIndex = textItem.startIndex;

    // Start reading from the hovered word
    setIsStoppedAndReady(false); // Exit stopped-and-ready mode
    speakFromPosition(
      text,
      wordStartIndex,
      [],
      documentInfo.currentPage - 1,
      (event) => {
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const charIndex = event.charIndex;
          const currentTextItem = textItems.find(
            (item) => charIndex >= item.startIndex && charIndex < item.endIndex
          );
          if (currentTextItem) {
            setCurrentHighlightedWord(currentTextItem);
          }
        }
      },
      () => {
        setCurrentHighlightedWord(null);
        // Re-enter stopped-and-ready mode only if we're still not speaking
        if (!speaking) {
          setIsStoppedAndReady(true);
        }
      }
    );
  }, [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]);

  const handleSentenceHover = useCallback(async (sentence: string, startIndex: number) => {
    if (!documentInfo || !documentInfo.pdfDoc) return;

    // Allow hover reading when audio is stopped or paused
    const canHover = !speaking || paused;
    if (!canHover) return;

    // Get the full text for the current page
    const { text, textItems } = await getPageTextAndItems(documentInfo.currentPage);

    // Start reading from the sentence
    setIsStoppedAndReady(false); // Exit stopped-and-ready mode
    speakFromPosition(
      text,
      startIndex,
      [],
      documentInfo.currentPage - 1,
      (event) => {
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const charIndex = event.charIndex;
          const currentTextItem = textItems.find(
            (item) => charIndex >= item.startIndex && charIndex < item.endIndex
          );
          if (currentTextItem) {
            setCurrentHighlightedWord(currentTextItem);
          }
        }
      },
      () => {
        setCurrentHighlightedWord(null);
        // Re-enter stopped-and-ready mode only if we're still not speaking
        if (!speaking) {
          setIsStoppedAndReady(true);
        }
      }
    );
  }, [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]);

  const handleParagraphHover = useCallback(async (paragraph: string, startIndex: number) => {
    if (!documentInfo || !documentInfo.pdfDoc) return;

    // Allow hover reading when audio is stopped or paused
    const canHover = !speaking || paused;
    if (!canHover) return;

    // Get the full text for the current page
    const { text, textItems } = await getPageTextAndItems(documentInfo.currentPage);

    // Start reading from the paragraph
    setIsStoppedAndReady(false); // Exit stopped-and-ready mode
    speakFromPosition(
      text,
      startIndex,
      [],
      documentInfo.currentPage - 1,
      (event) => {
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const charIndex = event.charIndex;
          const currentTextItem = textItems.find(
            (item) => charIndex >= item.startIndex && charIndex < item.endIndex
          );
          if (currentTextItem) {
            setCurrentHighlightedWord(currentTextItem);
          }
        }
      },
      () => {
        setCurrentHighlightedWord(null);
        // Re-enter stopped-and-ready mode only if we're still not speaking
        if (!speaking) {
          setIsStoppedAndReady(true);
        }
      }
    );
  }, [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]);

  const handleReadingComplete = useCallback(async () => {
    if (!documentInfo || !isAutoReading) return;

    const currentPage = documentInfo.currentPage;
    const nextPage = currentPage + 1;

    if (nextPage > documentInfo.totalPages) {
      // Reached the end of the document
      setIsAutoReading(false);
      stop();
      clearHighlights();
      setCurrentHighlight(null);
      setCurrentHighlightedWord(null);
      setReadWords(new Set());
      setIsStoppedAndReady(true);
    } else {
      // Move to the next page
      stop();
      setReadWords(new Set()); // Clear read words for new page
      await setCurrentPage(nextPage);
      setTimeout(() => {
        if (isAutoReading) {
          setIsAutoReading(true);
        }
      }, 500);
    }
  }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights]);

  useEffect(() => {
    if (
      !isAutoReading ||
      !documentInfo ||
      speaking ||
      isLoading ||
      !documentInfo.pdfDoc
    )
      return;

    const readingTimeout = setTimeout(async () => {
      const { text, textItems } = await getPageTextAndItems(
        documentInfo.currentPage
      );
      setSpreadTextItems(textItems);
      setCurrentHighlightedText(text);
      speak(
        text,
        [],
        documentInfo.currentPage - 1,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const textItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (textItem) {
              const word = text.substring(
                charIndex,
                charIndex + event.charLength
              ).trim(); // Trim only the extracted word, not the source text

              // Calculate precise word boundaries within the text item
              const wordStart = Math.max(charIndex, textItem.startIndex);
              const wordEnd = Math.min(
                charIndex + event.charLength,
                textItem.endIndex
              );

              // Ensure we don't have negative or zero lengths
              const textItemLength = Math.max(textItem.endIndex - textItem.startIndex, 1);
              const wordLength = Math.max(wordEnd - wordStart, 1);

              // Calculate the precise position and width of the spoken word
              const wordProgress = (wordStart - textItem.startIndex) / textItemLength;
              const wordLengthRatio = wordLength / textItemLength;

              // Ensure progress values are within valid bounds
              const clampedProgress = Math.max(0, Math.min(1, wordProgress));
              const clampedLengthRatio = Math.max(0.1, Math.min(1, wordLengthRatio));

              const preciseX = textItem.coordinates.x + (textItem.coordinates.width * clampedProgress);
              const preciseWidth = Math.max(textItem.coordinates.width * clampedLengthRatio, 15); // Minimum width for visibility

              const highlightItem = {
                ...textItem,
                text: word,
                startIndex: wordStart,
                endIndex: wordEnd,
                coordinates: {
                  ...textItem.coordinates,
                  x: preciseX,
                  width: preciseWidth,
                  height: textItem.coordinates.height + 4, // Slightly taller for better visibility
                },
              };

              setCurrentHighlight(highlightItem);
              setCurrentHighlightedWord(textItem);

              // Track read words for progress indication
              setReadWords(prev => new Set(prev).add(`${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`));
            }
          }
        },
        () => {
          setCurrentHighlight(null);
          setCurrentHighlightedWord(null);
          handleReadingComplete();
        }
      );
    }, 300);

    return () => clearTimeout(readingTimeout);
  }, [
    isAutoReading,
    documentInfo,
    speaking,
    isLoading,
    getPageTextAndItems,
    speak,
    handleReadingComplete,
  ]);

  return (
    <Box className="min-h-screen flex flex-col bg-gray-100">
      <Header onHistoryClick={() => {}} />
      <Container
        maxWidth={false}
        sx={{
          flexGrow: 1,
          py: { xs: 2, sm: 3, md: 4, lg: 4, xl: 4 },
          px: { xs: 1, sm: 1, md: 2, lg: 2, xl: 3 },
          display: "flex",
          flexDirection: "column",
          maxWidth: {
            xs: "100%",
            sm: "90%",
            md: "95%",
            lg: "90%",
            xl: "85%",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            gap: { xs: 1, sm: 2, md: 2, lg: 2, xl: 2 },
            height: "100%",
          }}
        >
          <Box
            sx={{
              width: {
                xs: "100%",
                sm: "100%",
                md: "35%",
                lg: "30%",
                xl: "30%",
              },
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
              <PageTitle title="Audio Controls" />
              {/* {isStoppedAndReady && (
                <Typography
                  variant="body2"
                  align="center"
                  sx={{
                    color: 'success.main',
                    fontWeight: 'bold',
                    mb: 1,
                    backgroundColor: 'success.light',
                    borderRadius: 1,
                    py: 0.5,
                    px: 1,
                  }}
                >
                  🎯 Hover over any word to start reading
                </Typography>
              )}
              {(!speaking || paused) && !isStoppedAndReady && (
                <Typography
                  variant="body2"
                  align="center"
                  sx={{
                    color: paused ? 'warning.main' : 'info.main',
                    fontWeight: 'bold',
                    mb: 1,
                    backgroundColor: paused ? 'warning.light' : 'info.light',
                    borderRadius: 1,
                    py: 0.5,
                    px: 1,
                  }}
                >
                  {paused ? '⏸️ Hover, click or highlight text to resume reading' : '📝 Hover, click or highlight text to start reading'}
                </Typography>
              )} */}
              <Paper
                sx={{
                  mt: 2,
                  bgcolor: "transparent",
                  p: { xs: 1, sm: 2, md: 2 },
                }}
              >
              <AudioControls
                playing={speaking}
                paused={paused}
                onPlay={() => {
                  setIsAutoReading(true);
                  setIsStoppedAndReady(false); // Disable hover-to-read mode
                }}
                onPause={() => {
                  pause();
                  // Enable hover-to-read mode when paused
                  setIsStoppedAndReady(true);
                }}
                onResume={() => {
                  resume();
                  // Disable hover-to-read mode when resuming
                  setIsStoppedAndReady(false);
                }}
                onStop={() => {
                  stop();
                  setIsAutoReading(false);
                  clearHighlights();
                  setCurrentHighlight(null);
                  setCurrentHighlightedWord(null);
                  setIsStoppedAndReady(true); // Enable hover-to-read mode
                }}
                onNext={async () => {
                  if (!documentInfo) return;
                  stop();
                  setIsAutoReading(false);
                  clearHighlights();
                  setCurrentHighlight(null);
                  setCurrentHighlightedWord(null);
                  setIsStoppedAndReady(false); // Disable hover-to-read mode
                  const nextPage = documentInfo.currentPage + 1;
                  if (nextPage <= documentInfo.totalPages) {
                    await setCurrentPage(nextPage);
                  }
                }}
                onPrevious={async () => {
                  if (!documentInfo) return;
                  stop();
                  setIsAutoReading(false);
                  clearHighlights();
                  setCurrentHighlight(null);
                  setCurrentHighlightedWord(null);
                  setIsStoppedAndReady(false); // Disable hover-to-read mode
                  const prevPage = documentInfo.currentPage - 1;
                  if (prevPage >= 1) {
                    await setCurrentPage(prevPage);
                  }
                }}
                voiceOptions={options}
                setVoiceOptions={setOptions}
                availableVoices={voices}
              />
            </Paper>
            </Paper>
            
          </Box>

          <Box
            sx={{
              width: {
                xs: "100%",
                sm: "100%",
                md: "65%",
                lg: "70%",
                xl: "70%",
              },
              flexGrow: 1,
            }}
            ref={containerRef}
          >
            
              <PDFViewer
                document={documentInfo}
                highlights={highlights}
                onPageChange={setCurrentPage}
                onTextSelection={handleTextSelection}
                currentHighlight={currentHighlight}
                pageHeight={documentInfo?.pageHeight || 0}
                isReading={isAutoReading || speaking}
                textItems={spreadTextItems}
                currentHighlightedWord={currentHighlightedWord}
                onWordClick={handleWordClick}
                onSentenceClick={handleSentenceClick}
                onParagraphClick={handleParagraphClick}
                onWordHover={handleWordHover}
                onSentenceHover={handleSentenceHover}
                onParagraphHover={handleParagraphHover}
                speaking={speaking}
                paused={paused}
                readWords={readWords}
              />
            
          </Box>
        </Box>
      </Container>
      <Footer />
    </Box>
  );
};

export default HomePage;