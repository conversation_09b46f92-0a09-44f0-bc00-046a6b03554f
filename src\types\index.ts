// export interface PDFDocument {
//   id: string;
//   name: string;
//   url: string;
//   totalPages: number;
//   currentPage: number;
//   dateAdded: Date;
// }

// export interface HighlightArea {
//   pageIndex: number;
//   text: string;
//   boundingRect?: {
//     x1: number;
//     y1: number;
//     x2: number;
//     y2: number;
//     width: number;
//     height: number;
//   };
// }

// export interface VoiceOptions {
//   rate: number;
//   pitch: number;
//   volume: number;
//   voice: SpeechSynthesisVoice | null;
// }

// export interface HighlightArea {
//   pageIndex: number;
//   text: string;
//   coordinates?: {
//     x: number;
//     y: number;
//     width: number;
//     height: number;
//   };
// }

// export interface ImageDescription {
//   pageIndex: number;
//   description: string;
//   coordinates?: {
//     x: number;
//     y: number;
//     width: number;
//     height: number;
//   };
// }

// export interface PDFDocument {
//   id: string;
//   name: string;
//   url: string;
//   totalPages: number;
//   currentPage: number;
//   dateAdded: Date;
//   imageDescriptions?: ImageDescription[];
// }

// export interface VoiceOptions {
//   rate: number;
//   pitch: number;
//   volume: number;
//   voice: SpeechSynthesisVoice | null;
// }

import * as pdfjs from "pdfjs-dist";

export interface HighlightArea {
  pageIndex: number;
  text: string;
  coordinates?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface ImageDescription {
  pageIndex: number;
  description: string;
  coordinates?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface PDFDocument {
  id: string;
  name: string;
  url: string;
  totalPages: number;
  currentPage: number;
  dateAdded: Date;
  imageDescriptions?: ImageDescription[];
  pdfDoc?: pdfjs.PDFDocumentProxy; // Add pdfDoc
}

export interface VoiceOptions {
  rate: number;
  pitch: number;
  volume: number;
  voice: SpeechSynthesisVoice | null;
}

export interface TextItem {
  pageIndex: number;
  text: string;
  startIndex: number;
  endIndex: number;
  coordinates: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface PDFDocument {
  id: string;
  name: string;
  url: string;
  totalPages: number;
  currentPage: number;
  dateAdded: Date;
  imageDescriptions?: ImageDescription[];
  pdfDoc?: pdfjs.PDFDocumentProxy;
  pageHeight?: number;
}
