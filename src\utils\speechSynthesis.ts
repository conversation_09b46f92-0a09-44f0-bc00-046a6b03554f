import type { VoiceOptions } from '../types';

/**
 * Speech synthesis utility functions
 */

// Get available voices
export const getAvailableVoices = (): SpeechSynthesisVoice[] => {
  return window.speechSynthesis.getVoices();
};

// Initialize default voice options
export const initVoiceOptions = (): VoiceOptions => {
  return {
    rate: 1,
    pitch: 1,
    volume: 1,
    voice: null
  };
};

// Speak text
export const speakText = (
  text: string, 
  options: VoiceOptions, 
  onBoundary?: (event: SpeechSynthesisEvent) => void,
  onEnd?: () => void
): SpeechSynthesisUtterance => {
  const utterance = new SpeechSynthesisUtterance(text);
  
  // Set voice options
  utterance.rate = options.rate;
  utterance.pitch = options.pitch;
  utterance.volume = options.volume;
  
  if (options.voice) {
    utterance.voice = options.voice;
  }
  
  // Add event listeners
  if (onBoundary) {
    utterance.onboundary = onBoundary;
  }
  
  if (onEnd) {
    utterance.onend = onEnd;
  }
  
  // Speak
  window.speechSynthesis.speak(utterance);
  
  return utterance;
};

// Pause speaking
export const pauseSpeaking = (): void => {
  window.speechSynthesis.pause();
};

// Resume speaking
export const resumeSpeaking = (): void => {
  window.speechSynthesis.resume();
};

// Stop speaking
export const stopSpeaking = (): void => {
  window.speechSynthesis.cancel();
};

// Check if speaking
export const isSpeaking = (): boolean => {
  return window.speechSynthesis.speaking;
};

// Check if paused
export const isPaused = (): boolean => {
  return window.speechSynthesis.paused;
};

// Update voice options
export const updateVoiceOptions = (
  currentOptions: VoiceOptions, 
  newOptions: Partial<VoiceOptions>
): VoiceOptions => {
  return {
    ...currentOptions,
    ...newOptions
  };
};